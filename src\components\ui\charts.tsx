'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>B<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>rts<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as RechartsPieChart,
  Area,
  AreaChart as RechartsAreaChart,
  Bar,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
  Pie
} from 'recharts';
import { chartStyles, chartConfigs, getChartColors } from '@/lib/chart-theme';

// Error Boundary Component for Charts
class ChartErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Chart rendering error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="w-full h-full flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <p>Chart rendering error</p>
            <button
              onClick={() => this.setState({ hasError: false })}
              className="mt-2 px-3 py-1 text-sm bg-primary text-primary-foreground rounded"
            >
              Retry
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Define a generic chart data type
interface ChartDataItem {
  [key: string]: string | number;
}

// Custom tooltip props type
interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    color: string;
    dataKey: string;
    value: number;
  }>;
  label?: string;
}

// Common props for all chart types
interface ChartProps {
  data: ChartDataItem[];
  index: string;
  className?: string;
  showAnimation?: boolean;
  showLegend?: boolean;
  showGridLines?: boolean;
  valueFormatter?: (value: number) => string;
}

// Bar chart specific props
interface BarChartProps extends ChartProps {
  categories: string[];
  colors?: string[];
  layout?: 'vertical' | 'horizontal';
}

// Line chart specific props
interface LineChartProps extends ChartProps {
  categories: string[];
  colors?: string[];
  curveType?: 'linear' | 'smooth';
}

// Area chart specific props
interface AreaChartProps extends ChartProps {
  categories: string[];
  colors?: string[];
  curveType?: 'linear' | 'smooth';
  fillOpacity?: number;
}

// Pie chart specific props
interface PieChartProps {
  data: ChartDataItem[];
  index: string;
  category: string;
  colors?: string[];
  className?: string;
  showAnimation?: boolean;
  showLegend?: boolean;
  valueFormatter?: (value: number) => string;
  innerRadius?: number;
  outerRadius?: number;
}

// Enhanced chart implementations using Recharts

export function BarChart({
  data,
  index,
  categories,
  colors,
  className,
  showAnimation = true,
  showLegend = true,
  showGridLines = true,
  valueFormatter = (value) => `${value}`,
  layout = 'horizontal'
}: BarChartProps) {
  // Use theme colors if none provided
  const chartColors = colors || getChartColors(categories.length);

  // Validate and clean data to prevent NaN errors
  const cleanData = React.useMemo(() => {
    if (!data || !Array.isArray(data)) return [];

    const cleaned = data.map(item => {
      const cleanItem = { ...item };

      // Clean ALL numeric values in the item, not just categories
      Object.keys(cleanItem).forEach(key => {
        const value = cleanItem[key];
        if (typeof value === 'number') {
          if (value === null || value === undefined || isNaN(value) || !isFinite(value)) {
            console.warn(`Chart data cleaning: Found invalid numeric value for key '${key}':`, value);
            cleanItem[key] = 0;
          }
        } else if (typeof value === 'string') {
          // Try to convert string numbers and check if they're valid
          const numValue = Number(value);
          if (!isNaN(numValue) && isFinite(numValue) && value.trim() !== '') {
            cleanItem[key] = numValue;
          }
        }
      });

      // Clean all numeric values in categories
      categories.forEach(category => {
        const value = item[category];
        if (value === null || value === undefined || isNaN(Number(value)) || !isFinite(Number(value))) {
          console.warn(`Chart data cleaning: Found invalid category value for '${category}':`, value);
          cleanItem[category] = 0;
        } else {
          cleanItem[category] = Number(value);
        }
      });

      // Ensure index field is valid
      if (!cleanItem[index] || cleanItem[index] === null || cleanItem[index] === undefined) {
        console.warn(`Chart data cleaning: Found invalid index value for '${index}':`, cleanItem[index]);
        cleanItem[index] = 'Unknown';
      }

      return cleanItem;
    });

    // Final validation pass - ensure absolutely no NaN/invalid values
    const finalCleaned = cleaned.map(item => {
      const safeItem = { ...item };
      
      // Double-check all properties
      Object.keys(safeItem).forEach(key => {
        const value = safeItem[key];
        if (typeof value === 'number' && (!Number.isFinite(value) || isNaN(value))) {
          console.error(`Chart data error: Still found NaN/infinite value after cleaning for key '${key}':`, value);
          safeItem[key] = 0;
        }
      });
      
      return safeItem;
    });

    console.log('Chart data after cleaning:', finalCleaned);
    return finalCleaned;
  }, [data, categories, index]);

  // Validate chart configuration to prevent NaN in margins/dimensions
  const safeMargin = React.useMemo(() => {
    const margin = chartConfigs.barChart.margin;
    return {
      top: Number.isFinite(margin.top) ? margin.top : 20,
      right: Number.isFinite(margin.right) ? margin.right : 30,
      left: Number.isFinite(margin.left) ? margin.left : 20,
      bottom: Number.isFinite(margin.bottom) ? margin.bottom : 5
    };
  }, []);

  // Final safety check: validate every single data point before rendering
  const ultraSafeData = React.useMemo(() => {
    return cleanData.map((item, itemIndex) => {
      const safeItem: Record<string, string | number> = {};
      
      Object.keys(item).forEach(key => {
        const value = item[key];
        if (typeof value === 'number') {
          if (!Number.isFinite(value) || isNaN(value)) {
            console.error(`CRITICAL: Found NaN/invalid number in final data validation at item ${itemIndex}, key '${key}':`, value);
            safeItem[key] = 0;
          } else {
            safeItem[key] = value;
          }
        } else if (typeof value === 'string') {
          safeItem[key] = value || '';
        } else {
          console.warn(`Unexpected value type in chart data at item ${itemIndex}, key '${key}':`, typeof value, value);
          safeItem[key] = String(value) || '';
        }
      });
      
      return safeItem;
    });
  }, [cleanData]);

  // Add error boundary logic inline
  const [hasRenderError, setHasRenderError] = React.useState(false);

  React.useEffect(() => {
    // Reset error state when data changes
    setHasRenderError(false);
  }, [ultraSafeData]);

  // Create a minimal fallback dataset for testing
  const fallbackData = React.useMemo(() => {
    return [
      { [index]: 'Test 1', [categories[0]]: 10 },
      { [index]: 'Test 2', [categories[0]]: 20 },
      { [index]: 'Test 3', [categories[0]]: 15 }
    ];
  }, [index, categories]);

  // Final validation before rendering - moved before early returns
  const finalData = React.useMemo(() => {
    const dataToUse = ultraSafeData.length > 0 ? ultraSafeData : fallbackData;

    // Ensure all data points have valid numeric values for categories
    const validatedData = dataToUse.map(item => {
      const validatedItem = { ...item };

      // Validate index field
      if (!validatedItem[index] || validatedItem[index] === null || validatedItem[index] === undefined) {
        validatedItem[index] = 'Unknown';
      }

      // Validate category fields
      categories.forEach(category => {
        const value = validatedItem[category];
        if (typeof value !== 'number' || !Number.isFinite(value) || isNaN(value)) {
          console.warn(`Invalid value for category ${category}:`, value, 'replacing with 0');
          validatedItem[category] = 0;
        }
      });

      return validatedItem;
    });

    console.log('Final validated data being passed to chart:', validatedData);
    return validatedData;
  }, [ultraSafeData, fallbackData, index, categories]);

  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div style={chartStyles.tooltip.contentStyle}>
          <p className="text-vista-light font-medium mb-2">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {`${entry.dataKey}: ${valueFormatter(entry.value || 0)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Don't render if no valid data
  if (!cleanData.length) {
    return (
      <div className={cn("w-full h-full flex items-center justify-center", className)}>
        <div className="text-center text-vista-light/50">
          <p>No data available</p>
        </div>
      </div>
    );
  }

  if (hasRenderError) {
    return (
      <div className={cn("w-full h-full flex items-center justify-center", className)}>
        <div className="text-center text-vista-light/50">
          <p>Chart rendering error - please refresh</p>
        </div>
      </div>
    );
  }

  return (
    <ChartErrorBoundary>
      <div className={cn("w-full h-full", className)}>
        <ResponsiveContainer width="100%" height="100%" minWidth={200} minHeight={150}>
          <RechartsBarChart
            data={finalData}
            layout={layout === 'vertical' ? 'horizontal' : 'vertical'}
            margin={safeMargin}
          >
            {showGridLines && (
              <CartesianGrid
                strokeDasharray={chartStyles.grid.strokeDasharray}
                stroke={chartStyles.grid.stroke}
                opacity={Number.isFinite(chartStyles.grid.opacity) ? chartStyles.grid.opacity : 0.3}
                horizontal={chartStyles.grid.horizontal}
                vertical={chartStyles.grid.vertical}
              />
            )}
            <XAxis
              dataKey={layout === 'vertical' ? (categories[0] || 'value') : (index || 'label')}
              type={layout === 'vertical' ? 'number' : 'category'}
              tick={chartStyles.axis.tick}
              axisLine={chartStyles.axis.axisLine}
              tickLine={chartStyles.axis.tickLine}
              domain={layout === 'vertical' ? [0, 'dataMax'] : undefined}
            />
            <YAxis
              dataKey={layout === 'vertical' ? (index || 'label') : (categories[0] || 'value')}
              type={layout === 'vertical' ? 'category' : 'number'}
              tick={chartStyles.axis.tick}
              axisLine={chartStyles.axis.axisLine}
              tickLine={chartStyles.axis.tickLine}
              width={layout === 'vertical' ? 80 : undefined}
              domain={layout === 'vertical' ? undefined : [0, 'dataMax']}
            />
            <Tooltip
              content={<CustomTooltip />}
              cursor={chartStyles.tooltip.cursor}
            />
            {showLegend && <Legend {...chartStyles.legend} />}
            {categories.map((category, i) => (
              <Bar
                key={category}
                dataKey={category}
                fill={chartColors[i % chartColors.length]}
                radius={[3, 3, 0, 0] as [number, number, number, number]}
                animationDuration={showAnimation && Number.isFinite(chartConfigs.barChart.animationDuration) ? chartConfigs.barChart.animationDuration : 0}
                animationBegin={showAnimation && Number.isFinite(chartConfigs.barChart.animationBegin + (i * 100)) ? chartConfigs.barChart.animationBegin + (i * 100) : 0}
              />
            ))}
          </RechartsBarChart>
        </ResponsiveContainer>
      </div>
    </ChartErrorBoundary>
  );
}

export function LineChart({
  data,
  index,
  categories,
  colors,
  className,
  showAnimation = true,
  showLegend = true,
  showGridLines = true,
  valueFormatter = (value) => `${value}`,
  curveType = 'linear'
}: LineChartProps) {
  // Use theme colors if none provided
  const chartColors = colors || getChartColors(categories.length);

  // Validate and clean data to prevent NaN errors
  const cleanData = React.useMemo(() => {
    if (!data || !Array.isArray(data)) return [];

    return data.map(item => {
      const cleanItem = { ...item };

      // Clean all numeric values in categories
      categories.forEach(category => {
        const value = item[category];
        if (value === null || value === undefined || isNaN(Number(value))) {
          cleanItem[category] = 0;
        } else {
          cleanItem[category] = Number(value);
        }
      });

      // Ensure index field is valid
      if (!cleanItem[index] || cleanItem[index] === null || cleanItem[index] === undefined) {
        cleanItem[index] = 'Unknown';
      }

      return cleanItem;
    });
  }, [data, categories, index]);

  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div style={chartStyles.tooltip.contentStyle}>
          <p className="text-vista-light font-medium mb-2">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {`${entry.dataKey}: ${valueFormatter(entry.value || 0)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Don't render if no valid data
  if (!cleanData.length) {
    return (
      <div className={cn("w-full h-full flex items-center justify-center", className)}>
        <div className="text-center text-vista-light/50">
          <p>No data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("w-full h-full", className)}>
      <ResponsiveContainer width="100%" height="100%" minWidth={200} minHeight={150}>
        <RechartsLineChart
          data={cleanData}
          margin={chartConfigs.lineChart.margin}
        >
          {showGridLines && (
            <CartesianGrid
              strokeDasharray={chartStyles.grid.strokeDasharray}
              stroke={chartStyles.grid.stroke}
              opacity={chartStyles.grid.opacity}
              horizontal={chartStyles.grid.horizontal}
              vertical={chartStyles.grid.vertical}
            />
          )}
          <XAxis
            dataKey={index}
            tick={chartStyles.axis.tick}
            axisLine={chartStyles.axis.axisLine}
            tickLine={chartStyles.axis.tickLine}
          />
          <YAxis
            tick={chartStyles.axis.tick}
            axisLine={chartStyles.axis.axisLine}
            tickLine={chartStyles.axis.tickLine}
          />
          <Tooltip
            content={<CustomTooltip />}
            cursor={chartStyles.tooltip.cursor}
          />
          {showLegend && <Legend {...chartStyles.legend} />}
          {categories.map((category, i) => (
            <Line
              key={category}
              type={curveType === 'smooth' ? 'monotone' : 'linear'}
              dataKey={category}
              stroke={chartColors[i % chartColors.length]}
              strokeWidth={chartConfigs.lineChart.strokeWidth}
              dot={{
                fill: chartColors[i % chartColors.length],
                strokeWidth: chartConfigs.lineChart.dot.strokeWidth,
                r: chartConfigs.lineChart.dot.r
              }}
              activeDot={{
                r: chartConfigs.lineChart.activeDot.r,
                stroke: chartColors[i % chartColors.length],
                strokeWidth: chartConfigs.lineChart.activeDot.strokeWidth,
                fill: '#ffffff'
              }}
              animationDuration={showAnimation ? chartConfigs.lineChart.animationDuration : 0}
            />
          ))}
        </RechartsLineChart>
      </ResponsiveContainer>
    </div>
  );
}

export function AreaChart({
  data,
  index,
  categories,
  colors,
  className,
  showAnimation = true,
  showLegend = true,
  showGridLines = true,
  valueFormatter = (value) => `${value}`,
  curveType = 'linear',
  fillOpacity = 0.3
}: AreaChartProps) {
  // Use theme colors if none provided
  const chartColors = colors || getChartColors(categories.length);

  // Validate and clean data to prevent NaN errors
  const cleanData = React.useMemo(() => {
    if (!data || !Array.isArray(data)) return [];

    return data.map(item => {
      const cleanItem = { ...item };

      // Clean all numeric values in categories
      categories.forEach(category => {
        const value = item[category];
        if (value === null || value === undefined || isNaN(Number(value))) {
          cleanItem[category] = 0;
        } else {
          cleanItem[category] = Number(value);
        }
      });

      // Ensure index field is valid
      if (!cleanItem[index] || cleanItem[index] === null || cleanItem[index] === undefined) {
        cleanItem[index] = 'Unknown';
      }

      return cleanItem;
    });
  }, [data, categories, index]);

  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div style={chartStyles.tooltip.contentStyle}>
          <p className="text-vista-light font-medium mb-2">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {`${entry.dataKey}: ${valueFormatter(entry.value || 0)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Don't render if no valid data
  if (!cleanData.length) {
    return (
      <div className={cn("w-full h-full flex items-center justify-center", className)}>
        <div className="text-center text-vista-light/50">
          <p>No data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("w-full h-full", className)}>
      <ResponsiveContainer width="100%" height="100%" minWidth={200} minHeight={150}>
        <RechartsAreaChart
          data={cleanData}
          margin={chartConfigs.areaChart.margin}
        >
          <defs>
            {categories.map((category, i) => (
              <linearGradient key={category} id={`gradient-${category}`} x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor={chartColors[i % chartColors.length]} stopOpacity={fillOpacity} />
                <stop offset="95%" stopColor={chartColors[i % chartColors.length]} stopOpacity={0} />
              </linearGradient>
            ))}
          </defs>
          {showGridLines && (
            <CartesianGrid
              strokeDasharray={chartStyles.grid.strokeDasharray}
              stroke={chartStyles.grid.stroke}
              opacity={chartStyles.grid.opacity}
              horizontal={chartStyles.grid.horizontal}
              vertical={chartStyles.grid.vertical}
            />
          )}
          <XAxis
            dataKey={index}
            tick={chartStyles.axis.tick}
            axisLine={chartStyles.axis.axisLine}
            tickLine={chartStyles.axis.tickLine}
          />
          <YAxis
            tick={chartStyles.axis.tick}
            axisLine={chartStyles.axis.axisLine}
            tickLine={chartStyles.axis.tickLine}
          />
          <Tooltip
            content={<CustomTooltip />}
            cursor={chartStyles.tooltip.cursor}
          />
          {showLegend && <Legend {...chartStyles.legend} />}
          {categories.map((category, i) => (
            <Area
              key={category}
              type={curveType === 'smooth' ? 'monotone' : 'linear'}
              dataKey={category}
              stroke={chartColors[i % chartColors.length]}
              strokeWidth={chartConfigs.areaChart.strokeWidth}
              fill={`url(#gradient-${category})`}
              animationDuration={showAnimation ? chartConfigs.areaChart.animationDuration : 0}
            />
          ))}
        </RechartsAreaChart>
      </ResponsiveContainer>
    </div>
  );
}

export function PieChart({
  data,
  index,
  category,
  colors,
  className,
  showAnimation = true,
  showLegend = true,
  valueFormatter = (value) => `${value}`,
  innerRadius = 0,
  outerRadius = 80
}: PieChartProps) {
  // Validate and clean data to prevent NaN errors
  const cleanData = React.useMemo(() => {
    if (!data || !Array.isArray(data)) return [];

    return data.filter(item => {
      // Filter out items with invalid values
      const value = item[category];
      const name = item[index];

      return (
        name !== null &&
        name !== undefined &&
        name !== '' &&
        value !== null &&
        value !== undefined &&
        !isNaN(Number(value)) &&
        Number(value) > 0
      );
    }).map(item => ({
      ...item,
      [category]: Number(item[category]),
      [index]: String(item[index])
    }));
  }, [data, category, index]);

  // Use theme colors if none provided
  const chartColors = colors || getChartColors(cleanData.length);

  const CustomTooltip = ({ active, payload }: { active?: boolean; payload?: Array<{ name: string; value: number; color: string; payload: { total: number } }> }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div style={chartStyles.tooltip.contentStyle}>
          <p className="text-vista-light font-medium mb-1">{data.name}</p>
          <p className="text-sm" style={{ color: data.color }}>
            {`Value: ${valueFormatter(data.value || 0)}`}
          </p>
          <p className="text-sm text-vista-light/70">
            {`${((data.value / (data.payload.total || 1)) * 100).toFixed(1)}%`}
          </p>
        </div>
      );
    }
    return null;
  };

  // Don't render if no valid data
  if (!cleanData.length) {
    return (
      <div className={cn("w-full h-full flex items-center justify-center", className)}>
        <div className="text-center text-vista-light/50">
          <p>No data available</p>
        </div>
      </div>
    );
  }

  // Calculate total for percentage calculation
  const total = cleanData.reduce((sum, item) => sum + Number(item[category]), 0);

  // Prepare data for Recharts
  const chartData = cleanData.map((item, i) => ({
    name: item[index],
    value: Number(item[category]),
    total,
    fill: chartColors[i % chartColors.length]
  }));

  return (
    <div className={cn("w-full h-full", className)}>
      <ResponsiveContainer width="100%" height="100%" minWidth={200} minHeight={150}>
        <RechartsPieChart margin={chartConfigs.pieChart.margin}>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            innerRadius={innerRadius || chartConfigs.pieChart.innerRadius}
            outerRadius={outerRadius || chartConfigs.pieChart.outerRadius}
            paddingAngle={chartConfigs.pieChart.paddingAngle}
            dataKey="value"
            animationDuration={showAnimation ? chartConfigs.pieChart.animationDuration : 0}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.fill} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          {showLegend && <Legend {...chartStyles.legend} />}
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
}
