'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { Loader2, UploadCloud, X, ChevronLeft, ChevronRight, ArrowLeft, Check, User, Eye, EyeOff } from 'lucide-react';
import { UserAvatar } from '@/components/UserAvatar';
import { cn } from '@/lib/utils';

interface UserFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  editUser?: {
    id: string;
    name: string;
    email: string;
    role: string;
    emailVerified?: Date | null;
    profileImage?: string;
    picture?: string;
  };
}

type FormStep = 'info' | 'security' | 'role' | 'confirm';

export default function UserFormModal({
  isOpen,
  onClose,
  onSuccess,
  editUser
}: UserFormModalProps) {
  const isEditing = !!editUser;
  const [currentStep, setCurrentStep] = useState<FormStep>(isEditing ? 'info' : 'info');
  
  // Progress percentages by step
  const stepProgress = {
    info: 25,
    security: isEditing ? 100 : 50, // Skip security if editing
    role: isEditing ? 66 : 75,
    confirm: 100
  };

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'user',
    emailVerified: false
  });
  
  // Password visibility states
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // State for image upload
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Update form data and reset image state when editing a user or opening the modal
  useEffect(() => {
    if (isOpen) {
      if (isEditing && editUser) {
        setFormData({
          name: editUser.name || '',
          email: editUser.email || '',
          password: '',
          confirmPassword: '',
          role: editUser.role || 'user',
          emailVerified: editUser.emailVerified ? true : false
        });
        // Set initial preview to existing image if editing
        const existingImageUrl = editUser.profileImage || editUser.picture;
        setImagePreview(existingImageUrl || null);
        setImageFile(null); // Reset file input
      } else {
        // Reset form when adding a new user
        setFormData({
          name: '',
          email: '',
          password: '',
          confirmPassword: '',
          role: 'user',
          emailVerified: false
        });
        setImagePreview(null);
        setImageFile(null);
      }
      // Always start at first step when opening
      setCurrentStep('info');
    } else {
      // Clean up preview URL when modal closes
      if (imagePreview && imagePreview.startsWith('blob:')) {
        URL.revokeObjectURL(imagePreview);
      }
      setImagePreview(null);
      setImageFile(null);
    }
  }, [isEditing, editUser, isOpen, imagePreview]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field when user types
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Toggle email verification status
  const handleVerificationToggle = (value: boolean) => {
    setFormData(prev => ({ ...prev, emailVerified: value }));
  };

  // Validate form based on current step
  const validateCurrentStep = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (currentStep === 'info') {
      if (!formData.name.trim()) {
        newErrors.name = 'Name is required';
      }

      if (!formData.email.trim()) {
        newErrors.email = 'Email is required';
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = 'Email is invalid';
      }
    }

    if (currentStep === 'security' && !isEditing) {
      if (!formData.password) {
        newErrors.password = 'Password is required';
      } else if (formData.password.length < 8) {
        newErrors.password = 'Password must be at least 8 characters';
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Validate entire form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Basic info validation
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    // Password validation (only for new users)
    if (!isEditing) {
      if (!formData.password) {
        newErrors.password = 'Password is required';
      } else if (formData.password.length < 8) {
        newErrors.password = 'Password must be at least 8 characters';
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Navigate to next step
  const goToNextStep = () => {
    if (!validateCurrentStep()) return;

    if (currentStep === 'info') {
      setCurrentStep(isEditing ? 'role' : 'security');
    } else if (currentStep === 'security') {
      setCurrentStep('role');
    } else if (currentStep === 'role') {
      setCurrentStep('confirm');
    }
  };

  // Navigate to previous step
  const goToPrevStep = () => {
    if (currentStep === 'security') {
      setCurrentStep('info');
    } else if (currentStep === 'role') {
      setCurrentStep(isEditing ? 'info' : 'security');
    } else if (currentStep === 'confirm') {
      setCurrentStep('role');
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    let uploadedImageUrl: string | null = null;
    const oldImageUrl: string | null = (isEditing && (editUser?.profileImage || editUser?.picture)) || null;
    let imageUpdateType: 'new' | 'remove' | 'none' = 'none';

    try {
      // 1. Handle Image Upload/Removal
      if (imageFile) {
        // New image selected for upload
        setIsUploading(true);
        try {
          const formData = new FormData();
          formData.append('file', imageFile);
          formData.append('upload_preset', process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET || 'streamvista_profiles'); // Use unsigned preset
          formData.append('cloud_name', process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || '');

          const cloudinaryUrl = `https://api.cloudinary.com/v1_1/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/image/upload`;

          const uploadResponse = await fetch(cloudinaryUrl, {
            method: 'POST',
            body: formData,
          });

          if (!uploadResponse.ok) {
            const errorData = await uploadResponse.json();
            throw new Error(`Cloudinary upload failed: ${errorData.error?.message || 'Unknown error'}`);
          }

          const uploadData = await uploadResponse.json();
          uploadedImageUrl = uploadData.secure_url;
          imageUpdateType = 'new';
          console.log('Cloudinary Upload Success:', uploadedImageUrl);

        } catch (uploadError) {
          console.error('Error uploading image:', uploadError);
          toast({
            title: 'Image Upload Failed',
            description: uploadError instanceof Error ? uploadError.message : 'Could not upload image.',
            variant: 'destructive',
          });
          setIsUploading(false);
          setIsSubmitting(false);
          return; // Stop submission if upload fails
        } finally {
          setIsUploading(false);
        }
      } else if (isEditing && oldImageUrl && !imagePreview) {
        // Image was present but removed by the user
        uploadedImageUrl = null; // Explicitly set to null for removal
        imageUpdateType = 'remove';
      }

      // 2. Prepare data for User API
      interface UserApiData {
        name: string;
        email: string;
        role: string;
        emailVerified: string | null;
        password?: string;
        profileImage?: string | null; // Can be new URL or null for removal
        oldProfileImage?: string | null; // To signal deletion on backend
      }

      const apiData: UserApiData & { userId?: string } = {
        name: formData.name,
        email: formData.email,
        role: formData.role,
        emailVerified: formData.emailVerified ? new Date().toISOString() : null,
        ...(formData.password && !isEditing ? { password: formData.password } : {}),
        // Include userId in the request body for authentication
        ...(userId ? { userId } : {})
      };

      if (imageUpdateType === 'new' && uploadedImageUrl) {
        apiData.profileImage = uploadedImageUrl;
        apiData.oldProfileImage = oldImageUrl; // Send old URL for deletion
      } else if (imageUpdateType === 'remove') {
        apiData.profileImage = null; // Signal removal
        apiData.oldProfileImage = oldImageUrl; // Send old URL for deletion
      }

      // 3. Determine API endpoint and method
      // Get user ID from cookie for authentication
      // Parse cookies properly to get userId
      const cookies = document.cookie.split('; ');
      const userIdCookie = cookies.find(cookie => cookie.startsWith('userId='));
      const userId = userIdCookie ? decodeURIComponent(userIdCookie.split('=')[1]) : null;
      
      // Log for debugging
      console.log('UserFormModal: Sending with userId:', userId);
      
      // Append userId as query parameter for additional authentication method
      const baseUrl = isEditing
        ? `/api/admin/users/${editUser.id}`
        : '/api/admin/users';
      // Make sure to include userId as a query parameter - this is critical for authentication
      const url = userId ? `${baseUrl}${baseUrl.includes('?') ? '&' : '?'}userId=${userId}` : baseUrl;
      const method = isEditing ? 'PUT' : 'POST';

      // 4. Make API request to save user data
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          // Include Authorization header with userId
          ...(userId ? { 'Authorization': `Bearer ${userId}` } : {}),
          // Add X-User-ID header as an additional authentication method
          ...(userId ? { 'X-User-ID': userId } : {})
        },
        body: JSON.stringify(apiData),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        // Attempt to clean up uploaded image if user save failed
        if (imageUpdateType === 'new' && uploadedImageUrl) {
          console.warn('User update failed after image upload. Attempting to delete uploaded image...');
          // TODO: Implement cleanup logic - call a backend route to delete the image from Cloudinary
        }
        throw new Error(errorData.error || 'Failed to save user');
      }

      // Show success message
      toast({
        title: isEditing ? 'User Updated' : 'User Created',
        description: isEditing
          ? `${formData.name}'s profile has been updated successfully.`
          : `${formData.name} has been added to the system.`,
        variant: 'success'
      });

      // Close modal and refresh user list
      onSuccess();
      onClose();

    } catch (error) {
      console.error('Error saving user:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
      setIsUploading(false); // Ensure uploading state is reset
    }
  };

  // Handle dialog close
  const handleDialogClose = () => {
    // Reset errors when dialog closes
    setErrors({});
    onClose();
  };

  // Handle image upload
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setImageFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle image removal
  const handleRemoveImage = () => {
    setImageFile(null);
    setImagePreview(null);
  };

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 'info':
  return (
          <div className="space-y-4">
            {/* Profile Image Upload */}
            <div className="space-y-2">
              <Label className="text-vista-light font-medium">Profile Image</Label>
              <div className="flex flex-col items-center gap-4">
                <UserAvatar
                  userId={editUser?.id || 'new'}
                  src={imagePreview || undefined}
                  alt={formData.name || 'User'}
                  fallback={formData.name?.charAt(0)?.toUpperCase() || 'U'}
                  className="h-24 w-24 rounded-full border-2 border-vista-light/30"
                />
                <div className="relative w-full max-w-xs">
                  <Input
                    id="profileImage"
                    type="file"
                    accept="image/png, image/jpeg, image/webp"
                    onChange={handleImageChange}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                    disabled={isUploading}
                  />
                  <div className="flex items-center justify-center w-full h-12 px-4 border-2 border-dashed border-vista-light/30 rounded-md bg-vista-dark-lighter hover:border-vista-blue transition-colors">
                    {isUploading ? (
                      <Loader2 className="h-6 w-6 text-vista-blue animate-spin" />
                    ) : imagePreview ? (
                      <div className="flex items-center justify-between w-full">
                        <span className="text-sm text-vista-light truncate">
                          {imageFile?.name || 'Current image'}
                        </span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={handleRemoveImage}
                          className="text-vista-light/70 hover:text-red-500 h-8 w-8 p-0"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <UploadCloud className="h-5 w-5 text-vista-light/70 mr-2" />
                        <span className="text-sm text-vista-light/70">Click or tap to upload</span>
                      </div>
                    )}
                  </div>
                </div>
                <p className="text-xs text-vista-light/60">Square image (PNG, JPG, WEBP). Max 2MB.</p>
              </div>
            </div>

            {/* Name */}
            <div className="space-y-2">
              <Label htmlFor="name" className="text-vista-light font-medium">Full Name</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Enter full name"
                className={cn(
                  "bg-vista-dark-lighter border-vista-light/20 text-vista-light focus:border-vista-blue focus:ring-1 focus:ring-vista-blue",
                  errors.name ? "border-red-500 focus:border-red-500 focus:ring-red-500" : ""
                )}
                autoComplete="off"
                autoFocus
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name}</p>
              )}
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-vista-light font-medium">Email Address</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
                className={cn(
                  "bg-vista-dark-lighter border-vista-light/20 text-vista-light focus:border-vista-blue focus:ring-1 focus:ring-vista-blue",
                  errors.email ? "border-red-500 focus:border-red-500 focus:ring-red-500" : ""
                )}
                autoComplete="off"
              />
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email}</p>
              )}
            </div>
          </div>
        );

      case 'security':
        return (
          <div className="space-y-4">
            {/* Password fields */}
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-vista-light font-medium">Password</Label>
              <div className="relative">
                  <Input
                    id="password"
                    name="password"
                  type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={handleChange}
                    placeholder="Create a password"
                  className={cn(
                    "bg-vista-dark-lighter border-vista-light/20 text-vista-light focus:border-vista-blue focus:ring-1 focus:ring-vista-blue pr-10",
                    errors.password ? "border-red-500 focus:border-red-500 focus:ring-red-500" : ""
                  )}
                    autoComplete="new-password"
                  />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-0 top-0 h-full px-3 text-vista-light/70 hover:text-vista-light"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  <span className="sr-only">
                    {showPassword ? "Hide password" : "Show password"}
                  </span>
                </Button>
              </div>
                  {errors.password && (
                    <p className="text-sm text-red-500">{errors.password}</p>
                  )}
                  {!errors.password && (
                    <p className="text-xs text-vista-light/60">Password must be at least 8 characters</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword" className="text-vista-light font-medium">Confirm Password</Label>
              <div className="relative">
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    placeholder="Confirm password"
                  className={cn(
                    "bg-vista-dark-lighter border-vista-light/20 text-vista-light focus:border-vista-blue focus:ring-1 focus:ring-vista-blue pr-10",
                    errors.confirmPassword ? "border-red-500 focus:border-red-500 focus:ring-red-500" : ""
                  )}
                    autoComplete="new-password"
                  />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-0 top-0 h-full px-3 text-vista-light/70 hover:text-vista-light"
                >
                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  <span className="sr-only">
                    {showConfirmPassword ? "Hide password" : "Show password"}
                  </span>
                </Button>
              </div>
                  {errors.confirmPassword && (
                    <p className="text-sm text-red-500">{errors.confirmPassword}</p>
                  )}
                </div>
          </div>
        );

      case 'role':
        return (
          <div className="space-y-4">
            {/* Role */}
            <div className="space-y-2">
              <Label htmlFor="role" className="text-vista-light font-medium">User Role</Label>
              <Select
                value={formData.role}
                onValueChange={(value) => handleSelectChange('role', value)}
              >
                <SelectTrigger
                  id="role"
                  className="bg-vista-dark-lighter border-vista-light/20 text-vista-light focus:border-vista-blue focus:ring-1 focus:ring-vista-blue"
                >
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent className="bg-vista-dark border-vista-light/20">
                  <SelectItem value="user" className="text-vista-light hover:bg-vista-dark-lighter focus:bg-vista-dark-lighter">Regular User</SelectItem>
                  <SelectItem value="moderator" className="text-vista-light hover:bg-vista-dark-lighter focus:bg-vista-dark-lighter">Moderator</SelectItem>
                  <SelectItem value="admin" className="text-vista-light hover:bg-vista-dark-lighter focus:bg-vista-dark-lighter">Administrator</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-vista-light/60 mt-2">
                Select the appropriate role for this user. Different roles have different permissions in the system.
              </p>
            </div>

            {/* Email Verification Status */}
            <div className="space-y-2 mt-4">
              <Label htmlFor="emailVerified" className="text-vista-light font-medium">Email Verification</Label>
              <Select
                value={formData.emailVerified ? 'verified' : 'unverified'}
                onValueChange={(value) => handleVerificationToggle(value === 'verified')}
              >
                <SelectTrigger
                  id="emailVerified"
                  className="bg-vista-dark-lighter border-vista-light/20 text-vista-light focus:border-vista-blue focus:ring-1 focus:ring-vista-blue"
                >
                  <SelectValue placeholder="Verification status" />
                </SelectTrigger>
                <SelectContent className="bg-vista-dark border-vista-light/20">
                  <SelectItem value="verified" className="text-vista-light hover:bg-vista-dark-lighter focus:bg-vista-dark-lighter">Verified</SelectItem>
                  <SelectItem value="unverified" className="text-vista-light hover:bg-vista-dark-lighter focus:bg-vista-dark-lighter">Unverified</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-vista-light/60">Set to verified to allow immediate login</p>
            </div>
          </div>
        );

      case 'confirm':
        return (
          <div className="space-y-4">
            <div className="p-4 bg-vista-dark-lighter rounded-lg">
              <h3 className="text-vista-light font-semibold mb-3">Review User Details</h3>
              
              <div className="flex items-center gap-3 mb-4">
                <UserAvatar
                  userId={editUser?.id || 'new'}
                  src={imagePreview || undefined}
                  alt={formData.name || 'User'}
                  fallback={formData.name?.charAt(0)?.toUpperCase() || 'U'}
                  className="h-16 w-16 rounded-full border-2 border-vista-light/30"
                />
                <div>
                  <h4 className="text-vista-light font-medium">{formData.name}</h4>
                  <p className="text-vista-light/70 text-sm">{formData.email}</p>
                </div>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-vista-light/70">Role:</span>
                  <span className="text-vista-light font-medium">{formData.role.charAt(0).toUpperCase() + formData.role.slice(1)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-vista-light/70">Email Status:</span>
                  <span className="text-vista-light font-medium">{formData.emailVerified ? 'Verified' : 'Unverified'}</span>
                </div>
                {!isEditing && (
                  <div className="flex justify-between">
                    <span className="text-vista-light/70">Password:</span>
                    <span className="text-vista-light font-medium">●●●●●●●●</span>
                  </div>
                )}
              </div>
            </div>
            
            <p className="text-sm text-vista-light/70 text-center">
              {isEditing 
                ? "Click 'Update User' to save these changes."
                : "Click 'Create User' to add this user to the system."}
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogContent className="sm:max-w-[500px] max-w-[95vw] bg-vista-dark border-vista-light/20 p-0 overflow-hidden">
        {/* Progress bar */}
        <div className="w-full bg-vista-dark-lighter h-1.5">
          <div 
            className="bg-vista-blue h-full transition-all duration-300 ease-in-out"
            style={{ width: `${stepProgress[currentStep]}%` }}
          ></div>
        </div>
        
        <DialogHeader className="p-4 sm:p-6 border-b border-vista-light/10">
          <div className="flex items-center">
            {currentStep !== 'info' && (
              <Button 
                type="button"
                variant="ghost"
                size="icon"
                onClick={goToPrevStep}
                className="mr-2 text-vista-light/70 hover:text-vista-light"
              >
                <ArrowLeft className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Button>
            )}
            <div className="flex-1">
              <DialogTitle className="text-vista-light">
                {isEditing ? 'Edit User' : 'Add New User'}
              </DialogTitle>
              <DialogDescription className="text-vista-light/70">
                {(() => {
                  switch(currentStep) {
                    case 'info': return 'Enter basic user information';
                    case 'security': return 'Set up user password';
                    case 'role': return 'Set up permissions';
                    case 'confirm': return 'Review and confirm details';
                    default: return '';
                  }
                })()}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="p-4 sm:p-6">
          {renderStepContent()}

          <div className="flex justify-between mt-6 pt-4 border-t border-vista-light/10">
            {currentStep !== 'info' ? (
              <Button
                type="button"
                variant="outline"
                onClick={goToPrevStep}
                className="border-vista-light/20 text-vista-light hover:bg-vista-dark-lighter hover:text-vista-light"
              >
                <ChevronLeft className="mr-1 h-4 w-4" />
                Back
              </Button>
            ) : (
            <Button
              type="button"
              variant="outline"
              onClick={handleDialogClose}
              className="border-vista-light/20 text-vista-light hover:bg-vista-dark-lighter hover:text-vista-light"
            >
              Cancel
            </Button>
            )}

            {currentStep !== 'confirm' ? (
              <Button
                type="button"
                onClick={goToNextStep}
                className="bg-vista-blue hover:bg-vista-blue/90 text-white"
              >
                Next
                <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            ) : (
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-vista-blue hover:bg-vista-blue/90 text-white"
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? 'Update User' : 'Create User'}
                {!isSubmitting && <Check className="ml-1 h-4 w-4" />}
            </Button>
            )}
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}