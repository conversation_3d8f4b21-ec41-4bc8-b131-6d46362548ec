'use client';

import { useState, useEffect, useMemo, ReactNode } from 'react';
import {
  Users,
  RefreshCw,
  Download,
  Calendar,
  Clock,
  Globe,
  Smartphone,
  Laptop,
  UserCheck,
  UserX,
  Info,
  BarChart3,
  MoreHorizontal,
  Settings,
  ExternalLink,
  MapPin,
  Eye,
  Trash2,
  Network,
  Activity,
  File,
  Tablet,
  Monitor,
  ArrowUpDown,
  CalendarDays,
  ChevronDown,
  Filter,
  Loader2,
  User,
  BarChart2 as BarChart,
  UserRound,
  Building,
  Map,
  AlertCircle
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger, TooltipElement } from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from '@/components/ui/use-toast';
import { useVisitorData, VisitorFilters as VisitorFilterParams } from '@/hooks/useVisitorData';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { useTabState } from '@/hooks/useTabState';
import VisitorFilters, { VisitorFilterOptions } from '@/components/admin/VisitorFilters';
import VisitorStats from '@/components/admin/VisitorStats';
import VisitorCharts from '@/components/admin/VisitorCharts';
import Pagination from '@/components/admin/Pagination';
import TimeRangeSelector from '@/components/admin/TimeRangeSelector';
import VisitorInsights from '@/components/admin/VisitorInsights';
import EngagementMetrics from '@/components/admin/EngagementMetrics';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import Link from 'next/link';
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Add visitor interface
interface AnonymousVisitor {
  _id: string;
  visitorId: string;
  nickname?: string;
  ipAddress?: string;
  userAgent?: string;
  firstVisit: Date | string;
  lastVisit: Date | string;
  visitCount: number;
  pagesViewed: number;
  referrer?: string;
  country?: string;
  countryCode?: string;
  region?: string;
  city?: string;
  timezone?: string;
  latitude?: number;
  longitude?: number;
  device?: string;
  browser?: string;
  os?: string;
  convertedToUser?: boolean;
  convertedUserId?: string;
  userId?: string;
  fingerprint?: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

export default function VisitorsPage() {
  // Use our custom hook to handle admin authentication
  const { isAuthorized, isLoading: authLoading } = useAdminAuth();

  // Use our custom tab state hook
  const { activeTab, tabsKey, handleTabChange } = useTabState('overview');

  // State for search, filters and pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [advancedFilters, setAdvancedFilters] = useState<VisitorFilterOptions>({
    search: '',
    dateRange: {
      from: null,
      to: null
    },
    country: [],
    device: [],
    browser: [],
    os: [],
    converted: null
  });

  // Add state for the delete confirmation dialog
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [visitorToDelete, setVisitorToDelete] = useState<string | null>(null);
  const [isDeleteAllDialogOpen, setIsDeleteAllDialogOpen] = useState(false);
  
  // Add state for view details modal
  const [selectedVisitor, setSelectedVisitor] = useState<AnonymousVisitor | null>(null);
  const [isViewDetailsOpen, setIsViewDetailsOpen] = useState(false);

  // Create memoized filters object for useVisitorData hook to prevent unnecessary re-renders
  const filters = useMemo<VisitorFilterParams>(() => ({
    page: currentPage,
    limit: 10,
    search: searchQuery,
    dateFrom: advancedFilters.dateRange.from ? advancedFilters.dateRange.from.toISOString() : null,
    dateTo: advancedFilters.dateRange.to ? advancedFilters.dateRange.to.toISOString() : null,
    country: advancedFilters.country.length === 1 ? advancedFilters.country[0] : '',
    device: advancedFilters.device.length === 1 ? advancedFilters.device[0] : '',
    browser: advancedFilters.browser.length === 1 ? advancedFilters.browser[0] : '',
    os: advancedFilters.os.length === 1 ? advancedFilters.os[0] : '',
    converted: advancedFilters.converted,
    sortBy: 'lastVisit',
    sortOrder: 'desc',
    compareToPrevious: true
  }), [
    currentPage,
    searchQuery,
    advancedFilters.dateRange.from,
    advancedFilters.dateRange.to,
    advancedFilters.country,
    advancedFilters.device,
    advancedFilters.browser,
    advancedFilters.os,
    advancedFilters.converted
  ]);

  // Fetch visitors data
  const {
    data: visitorData,
    isLoading,
    isAuthenticated,
    error,
    refetch,
    fetchTimeRange
  } = useVisitorData(filters);
  
  // Check if authentication and data fetch are properly synchronized
  const dataUnavailable = !isAuthenticated || !visitorData || error;

  // Extract visitors and pagination info
  const visitors = visitorData?.visitors || [];
  const pagination = visitorData?.pagination || { page: 1, limit: 10, total: 0, pages: 1 };
  const stats = visitorData?.stats || {
    totalVisitors: 0,
    totalVisits: 0,
    totalPagesViewed: 0,
    convertedCount: 0,
    averageVisits: 0,
    averagePagesViewed: 0,
    percentNewVisitors: 0,
    bounceRate: 0,
    averageSessionTime: 0
  };
  const distributions = visitorData?.distributions || {
    device: [],
    browser: [],
    os: [],
    country: []
  };
  const dailyVisitors = visitorData?.dailyVisitors || [];
  const insights = visitorData?.insights;
  const engagement = visitorData?.engagement;

  // Listen for authentication changes that might require a refetch
  useEffect(() => {
    if (isAuthenticated) {
      // Refetch on authentication change
      refetch();
    }
  }, [isAuthenticated, refetch]);
  
  // Add effect to check auth status periodically
  useEffect(() => {
    // Check auth on mount
    const checkAuth = () => {
      const userId = localStorage.getItem('userId');
      const userJson = localStorage.getItem('user');
      
      if (!userId || !userJson) {
        console.log('No user data found in localStorage');
        return false;
      }
      
      try {
        const userData = JSON.parse(userJson);
        return userData.role === 'admin' || userData.role === 'superadmin';
      } catch (e) {
        console.error('Error parsing user data:', e);
        return false;
      }
    };
    
    // Initial check
    if (!isAuthenticated && checkAuth()) {
      refetch();
    }
    
    // Set up interval to check auth status every 30 seconds
    const authCheckInterval = setInterval(() => {
      if (checkAuth() && !isLoading) {
        refetch();
      }
    }, 30000);
    
    return () => clearInterval(authCheckInterval);
  }, [isAuthenticated, isLoading, refetch]);

  // Update filters when search query changes
  useEffect(() => {
    setAdvancedFilters(prev => ({
      ...prev,
      search: searchQuery
    }));
  }, [searchQuery]);

  // Handle filter changes
  const handleFilterChange = (filters: VisitorFilterOptions) => {
    setAdvancedFilters(filters);

    // Update the search query to match the filter search
    if (searchQuery !== filters.search) {
      setSearchQuery(filters.search);
    }

    // Reset to first page when filters change
    setCurrentPage(1);

    // Refetch with new filters
    refetch();
  };

  // Reset filters
  const handleResetFilters = () => {
    setSearchQuery('');
    setAdvancedFilters({
      search: '',
      dateRange: {
        from: null,
        to: null
      },
      country: [],
      device: [],
      browser: [],
      os: [],
      converted: null
    });
    refetch();
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  // Handle time range selection
  const handleTimeRangeChange = (range: { from: Date | null; to: Date | null }) => {
    // Update the advanced filters
    setAdvancedFilters(prev => ({
      ...prev,
      dateRange: range
    }));
    
    // Fetch data for the selected time range
    fetchTimeRange(range.from, range.to);
  };

  // Format date for display
  const formatDate = (dateValue: string | Date) => {
    const date = dateValue instanceof Date ? dateValue : new Date(dateValue);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format time for display
  const formatTime = (dateValue: string | Date) => {
    const date = dateValue instanceof Date ? dateValue : new Date(dateValue);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format relative time
  const formatRelativeTime = (dateValue: string | Date) => {
    const date = dateValue instanceof Date ? dateValue : new Date(dateValue);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffSec < 60) {
      return 'just now';
    } else if (diffMin < 60) {
      return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
    } else if (diffHour < 24) {
      return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
    } else if (diffDay < 7) {
      return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
    } else {
      return formatDate(date);
    }
  };

  // Handle export visitors
  const handleExportVisitors = async (format: 'json' | 'csv') => {
    try {
      // Build query string from current filters
      const queryParams = new URLSearchParams();

      // Add userId to query string for authentication
      const userId = localStorage.getItem('userId');
      if (userId) {
        queryParams.append('userId', userId);
      }

      if (filters.search) queryParams.append('search', filters.search);
      if (filters.dateFrom) queryParams.append('dateFrom', filters.dateFrom);
      if (filters.dateTo) queryParams.append('dateTo', filters.dateTo);
      if (filters.country) queryParams.append('country', filters.country);
      if (filters.device) queryParams.append('device', filters.device);
      if (filters.browser) queryParams.append('browser', filters.browser);
      if (filters.os) queryParams.append('os', filters.os);
      if (filters.converted !== null && filters.converted !== undefined) {
        queryParams.append('converted', filters.converted.toString());
      }
      queryParams.append('format', format);

      // Create export URL with format parameter
      const exportUrl = `/api/admin/visitors/export?${queryParams.toString()}`;

      // Open in a new tab
      window.open(exportUrl, '_blank');

      toast({
        title: 'Export Started',
        description: `Visitors are being exported as ${format.toUpperCase()}.`,
        variant: 'success'
      });
    } catch (error) {
      console.error('Error exporting visitors:', error);
      toast({
        title: 'Export Failed',
        description: error instanceof Error ? error.message : 'Failed to export visitors',
        variant: 'destructive'
      });
    }
  };

  // Handle migrating visitors to add nicknames
  const handleMigrateVisitors = async () => {
    try {
      // Get userId for authentication
      const userId = localStorage.getItem('userId');
      if (!userId) {
        throw new Error('User ID not found');
      }

      // Call the migration API
      const response = await fetch(`/api/admin/visitors/migrate?userId=${userId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to migrate visitors');
      }

      const data = await response.json();

      // Show success toast
      toast({
        title: 'Migration Successful',
        description: data.message,
        variant: 'success'
      });

      // Refresh the data
      refetch();
    } catch (error) {
      console.error('Error migrating visitors:', error);
      toast({
        title: 'Migration Failed',
        description: error instanceof Error ? error.message : 'Failed to migrate visitors',
        variant: 'destructive'
      });
    }
  };

  // Handle deduplicating visitors
  const handleDeduplicateVisitors = async () => {
    try {
      if (!isAuthorized) {
        toast({
          title: "Unauthorized",
          description: "You don't have permission to perform this action",
          variant: "destructive"
        });
        return;
      }

      // Show confirmation first
      const confirmDeduplicate = window.confirm(
        "This operation will deduplicate visitor records based on IP addresses. " +
        "Each IP address will be treated as a unique visitor, and duplicate records will be merged. " +
        "This action cannot be undone. Do you want to continue?"
      );
      
      if (!confirmDeduplicate) return;
      
      // User confirmed, proceed with deduplication
      toast({
        title: "Processing",
        description: "Deduplicating visitor records based on IP addresses. This may take a moment...",
      });
      
      // Get user ID from localStorage for authentication
      const userId = localStorage.getItem('userId');
      if (!userId) {
        toast({
          title: "Authentication Error",
          description: "Could not authenticate your session",
          variant: "destructive"
        });
        return;
      }
      
      // Call the deduplicate API endpoint
      const response = await fetch(`/api/admin/visitors/deduplicate?userId=${userId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to deduplicate visitors');
      }
      
      const result = await response.json();
      
      toast({
        title: "Success",
        description: `Removed ${result.removedCount} duplicate records and merged ${result.mergedCount} visitor records.`,
      });
      
      // Refresh data
      await refetch();
    } catch (error) {
      console.error('Error deduplicating visitors:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to deduplicate visitor records",
        variant: "destructive"
      });
    }
  };

  // Handle updating visitor locations
  const handleUpdateLocations = async (force = false, batchSize = 25) => {
    try {
      // Get userId for authentication
      const userId = localStorage.getItem('userId');
      if (!userId) {
        throw new Error('User ID not found');
      }

      // Show starting toast
      toast({
        title: 'Updating Visitor Locations',
        description: `Starting location update for ${batchSize} visitors${force ? ' (forced update)' : ''}...`,
        variant: 'default'
      });

      // Call the update locations API with parameters
      const response = await fetch(
        `/api/admin/visitors/update-locations?userId=${userId}&force=${force}&batchSize=${batchSize}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update visitor locations');
      }

      const data = await response.json();

      // Show success toast with detailed information
      toast({
        title: 'Location Update Successful',
        description: data.message,
        variant: 'success'
      });

      // If there are remaining visitors, show a message
      if (data.remainingCount > 0) {
        toast({
          title: 'More Updates Available',
          description: `${data.remainingCount} visitors still need location updates. Run the update again to process more.`,
          variant: 'default'
        });
      }

      // Refresh the data
      refetch();

      return data;
    } catch (error) {
      console.error('Error updating visitor locations:', error);
      toast({
        title: 'Location Update Failed',
        description: error instanceof Error ? error.message : 'Failed to update visitor locations',
        variant: 'destructive'
      });
      return null;
    }
  };

  // Handle generating visitor report
  const handleGenerateReport = () => {
    try {
      // Get userId for authentication
      const userId = localStorage.getItem('userId');
      if (!userId) {
        throw new Error('User ID not found');
      }

      // Open report in new tab
      window.open(`/api/admin/visitors/report?userId=${userId}`, '_blank');

      toast({
        title: 'Generating Report',
        description: 'The visitor report is being generated in a new tab.',
        variant: 'success'
      });
    } catch (error) {
      console.error('Error generating visitor report:', error);
      toast({
        title: 'Report Generation Failed',
        description: error instanceof Error ? error.message : 'Failed to generate visitor report',
        variant: 'destructive'
      });
    }
  };

  // Handle deleting a visitor
  const handleDeleteVisitor = async (visitorId: string) => {
    try {
      // Get userId for authentication
      const userId = localStorage.getItem('userId');
      if (!userId) {
        throw new Error('User ID not found');
      }

      // Call the delete API
      const response = await fetch(`/api/admin/visitors/delete?userId=${userId}&visitorId=${visitorId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete visitor');
      }

      const data = await response.json();

      // Show success toast
      toast({
        title: 'Visitor Deleted',
        description: data.message,
        variant: 'success'
      });

      // Refresh the data
      refetch();
    } catch (error) {
      console.error('Error deleting visitor:', error);
      toast({
        title: 'Deletion Failed',
        description: error instanceof Error ? error.message : 'Failed to delete visitor',
        variant: 'destructive'
      });
    }
  };

  // Handle deleting all visitors
  const handleDeleteAllVisitors = async () => {
    try {
      // Get userId for authentication
      const userId = localStorage.getItem('userId');
      if (!userId) {
        throw new Error('User ID not found');
      }

      // Show starting toast
      toast({
        title: 'Deleting All Visitors',
        description: 'This may take a moment...',
        variant: 'default'
      });

      // Call the delete API with deleteAll=true
      const response = await fetch(
        `/api/admin/visitors/delete?userId=${userId}&deleteAll=true&confirm=true`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete all visitors');
      }

      const data = await response.json();

      // Show success toast
      toast({
        title: 'All Visitors Deleted',
        description: data.message,
        variant: 'success'
      });

      // Refresh the data
      refetch();
    } catch (error) {
      console.error('Error deleting all visitors:', error);
      toast({
        title: 'Deletion Failed',
        description: error instanceof Error ? error.message : 'Failed to delete all visitors',
        variant: 'destructive'
      });
    }
  };

  // Prepare filter options from distributions
  const countryOptions = distributions.country.map(item => ({
    label: item._id || 'Unknown',
    value: item._id || 'Unknown'
  }));

  const deviceOptions = distributions.device.map(item => ({
    label: item._id || 'Unknown',
    value: item._id || 'Unknown'
  }));

  const browserOptions = distributions.browser.map(item => ({
    label: item._id || 'Unknown',
    value: item._id || 'Unknown'
  }));

  const osOptions = distributions.os.map(item => ({
    label: item._id || 'Unknown',
    value: item._id || 'Unknown'
  }));

  // If not authorized, show loading or unauthorized message
  if (authLoading) {
    return (
      <div className="flex items-center justify-center h-[80vh]">
        <Loader2 className="h-8 w-8 animate-spin text-vista-blue" />
        <span className="ml-2">Checking authorization...</span>
      </div>
    );
  }

  if (!isAuthorized) {
    return (
      <div className="flex flex-col items-center justify-center h-[80vh] text-center">
        <UserX className="h-16 w-16 text-red-500 mb-4" />
        <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
        <p className="text-muted-foreground max-w-md mb-6">
          You do not have permission to access the admin dashboard. Please contact an administrator if you believe this is an error.
        </p>
        <Button asChild>
          <Link href="/">Return to Homepage</Link>
        </Button>
      </div>
    );
  }
  
  // Check for authentication errors from the visitor data hook
  if (error && !isLoading) {
    return (
      <div className="container mx-auto py-6 max-w-[1600px]">
        <div className="flex flex-col gap-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Visitor Analytics</h1>
              <p className="text-muted-foreground">
                Manage and analyze visitor data from your streaming platform.
              </p>
            </div>
          </div>
          
          <Card className="p-6">
            <div className="flex flex-col items-center justify-center py-10 text-center">
              <AlertCircle className="h-16 w-16 text-red-500 mb-4" />
              <h2 className="text-2xl font-bold mb-2">Authentication Error</h2>
              <p className="text-muted-foreground max-w-md mb-6">
                {error}
              </p>
              <Button onClick={() => refetch()} className="mb-2">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
              <Button variant="outline" asChild className="mt-2">
                <Link href="/">Return to Homepage</Link>
              </Button>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto py-4 px-2 w-full">
      <div className="flex flex-col gap-6">
        {/* Mobile-Optimized Header */}
        <div className="flex flex-col gap-4">
          <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
            <div className="flex-1">
              <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Visitor Analytics</h1>
              <p className="text-sm sm:text-base text-muted-foreground mt-1">
                Manage and analyze visitor data from your streaming platform.
              </p>
            </div>

            {/* Mobile-friendly controls */}
            <div className="flex flex-col sm:flex-row items-stretch sm:items-end gap-2 w-full sm:w-auto">
              <div className="w-full sm:w-auto">
                <TimeRangeSelector
                  onChange={handleTimeRangeChange}
                  initialRange={advancedFilters.dateRange}
                />
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch(true)}
                className="h-9 w-full sm:w-9 justify-center"
                title="Refresh data"
              >
                <RefreshCw className="h-4 w-4" />
                <span className="ml-2 sm:hidden">Refresh</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <VisitorStats stats={stats} isLoading={isLoading} />

        {/* Mobile-Optimized Tabs */}
        <Tabs
          key={tabsKey}
          defaultValue="overview"
          value={activeTab}
          onValueChange={handleTabChange}
          className="space-y-4 w-full"
        >
          <div className="w-full overflow-x-auto pb-1">
            <TabsList className="bg-muted/50 backdrop-blur-sm border border-vista-light/10 h-12 p-1 grid grid-cols-4 w-full min-w-[320px] sm:w-auto sm:inline-flex sm:min-w-0" key={`${tabsKey}-list`}>
              <TabsTrigger
                value="overview"
                className="flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-1.5 h-10 px-2 sm:px-4 text-xs sm:text-sm font-medium transition-all data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
              >
                <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                <span className="truncate">Overview</span>
              </TabsTrigger>
              <TabsTrigger
                value="insights"
                className="flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-1.5 h-10 px-2 sm:px-4 text-xs sm:text-sm font-medium transition-all data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
              >
                <Activity className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                <span className="truncate">Insights</span>
              </TabsTrigger>
              <TabsTrigger
                value="engagement"
                className="flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-1.5 h-10 px-2 sm:px-4 text-xs sm:text-sm font-medium transition-all data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
              >
                <UserCheck className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                <span className="truncate hidden xs:block">Engagement</span>
                <span className="truncate xs:hidden">Engage</span>
              </TabsTrigger>
              <TabsTrigger
                value="visitors"
                className="flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-1.5 h-10 px-2 sm:px-4 text-xs sm:text-sm font-medium transition-all data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
              >
                <Users className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                <span className="truncate">Visitors</span>
              </TabsTrigger>
            </TabsList>
          </div>
          
          {/* Overview Tab Content */}
          <TabsContent value="overview" className="space-y-6">
            <VisitorCharts 
              distributions={distributions} 
              dailyVisitors={dailyVisitors}
              isLoading={isLoading} 
            />
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              <Card className="border-2 border-slate-100/10 shadow-lg hover:shadow-xl transition-all duration-200">
                <CardHeader className="pb-3 sm:pb-0">
                  <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                    <Map className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
                    Top Locations
                  </CardTitle>
                  <CardDescription className="text-xs sm:text-sm">Where your visitors are from</CardDescription>
                </CardHeader>
                <CardContent className="pt-3 sm:pt-6">
                  {isLoading ? (
                    <div className="space-y-2">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <div key={i} className="flex justify-between animate-pulse">
                          <div className="h-4 sm:h-5 w-20 sm:w-24 bg-muted rounded"></div>
                          <div className="h-4 sm:h-5 w-12 sm:w-16 bg-muted rounded"></div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-3 sm:space-y-4">
                      {distributions.country.slice(0, 5).map((item, i) => (
                        <div key={i} className="flex items-center justify-between">
                          <div className="flex items-center gap-2 min-w-0 flex-1">
                            <Globe className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground flex-shrink-0" />
                            <span className="text-sm sm:text-base truncate">{item._id || 'Unknown'}</span>
                          </div>
                          <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0">
                            <span className="font-medium text-sm sm:text-base">{item.count.toLocaleString()}</span>
                            <Badge variant="outline" className="text-xs px-1 sm:px-2">
                              {((item.count / stats.totalVisitors) * 100).toFixed(1)}%
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="border-2 border-slate-100/10 shadow-lg hover:shadow-xl transition-all duration-200">
                <CardHeader className="pb-3 sm:pb-0">
                  <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                    <Building className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
                    Device Distribution
                  </CardTitle>
                  <CardDescription className="text-xs sm:text-sm">Devices used by your visitors</CardDescription>
                </CardHeader>
                <CardContent className="pt-3 sm:pt-6">
                  {isLoading ? (
                    <div className="space-y-2">
                      {Array.from({ length: 3 }).map((_, i) => (
                        <div key={i} className="flex justify-between animate-pulse">
                          <div className="h-4 sm:h-5 w-16 sm:w-20 bg-muted rounded"></div>
                          <div className="h-4 sm:h-5 w-12 sm:w-16 bg-muted rounded"></div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-3 sm:space-y-4">
                      {distributions.device.slice(0, 3).map((item, i) => {
                        const DeviceIcon =
                          item._id?.toLowerCase().includes('mobile') ? Smartphone :
                          item._id?.toLowerCase().includes('tablet') ? Tablet : Monitor;

                        return (
                          <div key={i} className="flex items-center justify-between">
                            <div className="flex items-center gap-2 min-w-0 flex-1">
                              <DeviceIcon className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground flex-shrink-0" />
                              <span className="text-sm sm:text-base truncate">{item._id || 'Unknown'}</span>
                            </div>
                            <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0">
                              <span className="font-medium text-sm sm:text-base">{item.count.toLocaleString()}</span>
                              <Badge variant="outline" className="text-xs px-1 sm:px-2">
                                {((item.count / stats.totalVisitors) * 100).toFixed(1)}%
                              </Badge>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          {/* Mobile-Optimized Insights Tab Content */}
          <TabsContent value="insights" className="space-y-4 sm:space-y-6 py-2">
            {isLoading ? (
              <div className="flex items-center justify-center p-8 sm:p-12">
                <Loader2 className="h-8 w-8 sm:h-10 sm:w-10 animate-spin text-primary" />
                <span className="ml-3 text-base sm:text-lg">Loading insights data...</span>
              </div>
            ) : !insights ? (
              <Card className="p-4 sm:p-8 border-2 border-slate-100/10 shadow-lg">
                <div className="flex flex-col items-center justify-center py-8 sm:py-12 text-center">
                  <AlertCircle className="h-12 w-12 sm:h-16 sm:w-16 text-amber-500 mb-4 sm:mb-6" />
                  <h3 className="text-xl sm:text-2xl font-medium mb-3 sm:mb-4">No insights data available</h3>
                  <p className="text-muted-foreground max-w-md mb-4 sm:mb-6 text-sm sm:text-lg">
                    There may not be enough visitor data to generate insights or there was an error loading the data.
                  </p>
                  <Button onClick={() => refetch(true)} size="default" className="px-4 sm:px-6">
                    <RefreshCw className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                    Refresh Data
                  </Button>
                </div>
              </Card>
            ) : (
              <div className="p-1 sm:p-2">
                <VisitorInsights data={insights} isLoading={isLoading} />
              </div>
            )}
          </TabsContent>
          
          {/* Mobile-Optimized Engagement Tab Content */}
          <TabsContent value="engagement" className="space-y-4 sm:space-y-6 py-2">
            {isLoading ? (
              <div className="flex items-center justify-center p-8 sm:p-12">
                <Loader2 className="h-8 w-8 sm:h-10 sm:w-10 animate-spin text-primary" />
                <span className="ml-3 text-base sm:text-lg">Loading engagement data...</span>
              </div>
            ) : !engagement ? (
              <Card className="p-4 sm:p-8 border-2 border-slate-100/10 shadow-lg">
                <div className="flex flex-col items-center justify-center py-8 sm:py-12 text-center">
                  <AlertCircle className="h-12 w-12 sm:h-16 sm:w-16 text-amber-500 mb-4 sm:mb-6" />
                  <h3 className="text-xl sm:text-2xl font-medium mb-3 sm:mb-4">No engagement data available</h3>
                  <p className="text-muted-foreground max-w-md mb-4 sm:mb-6 text-sm sm:text-lg">
                    There may not be enough visitor data to calculate engagement metrics or there was an error loading the data.
                  </p>
                  <Button onClick={() => refetch(true)} size="default" className="px-4 sm:px-6">
                    <RefreshCw className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                    Refresh Data
                  </Button>
                </div>
              </Card>
            ) : (
              <div className="p-1 sm:p-2">
                <EngagementMetrics data={engagement} isLoading={isLoading} />
              </div>
            )}
          </TabsContent>
          
          {/* Mobile-Optimized Visitors List Tab Content */}
          <TabsContent value="visitors" className="space-y-4 sm:space-y-6 py-2 max-w-full">
            <Card className="border-2 border-slate-100/10 shadow-lg overflow-hidden w-full">
              <CardHeader className="pb-3 sm:pb-4 bg-slate-800/30">
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <CardTitle className="text-lg sm:text-xl font-medium flex items-center gap-2">
                      <Users className="h-4 w-4 sm:h-5 sm:w-5 text-primary flex-shrink-0" />
                      <span className="truncate">Visitor Records</span>
                    </CardTitle>
                    <TooltipElement
                      content="Visitors are identified using IP address, browser fingerprint, and device information. Visitors from the same IP but with different devices are considered unique."
                      className="max-w-xs"
                    >
                      <Info className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground cursor-help flex-shrink-0" />
                    </TooltipElement>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="border-slate-700 hover:bg-slate-700/50 w-full sm:w-auto">
                        <MoreHorizontal className="h-4 w-4 mr-1" />
                        <span className="sm:sr-only">Actions</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      className="w-56 z-[9999] border border-vista-light/20 bg-vista-dark shadow-xl rounded-lg overflow-hidden max-h-[70vh] overflow-y-auto"
                      sideOffset={4}
                      collisionPadding={40}
                      avoidCollisions={true}
                      sticky="partial"
                      hideWhenDetached={false}
                      alignOffset={-8}
                    >
                      <DropdownMenuLabel>Visitor Data Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />

                      <DropdownMenuItem onClick={() => handleExportVisitors('csv')}>
                        <Download className="h-4 w-4 mr-2" />
                        <span>Export to CSV</span>
                      </DropdownMenuItem>

                      <DropdownMenuItem onClick={() => handleExportVisitors('json')}>
                        <File className="h-4 w-4 mr-2" />
                        <span>Export to JSON</span>
                      </DropdownMenuItem>

                      <DropdownMenuSeparator />

                      <DropdownMenuItem onClick={() => handleUpdateLocations()}>
                        <Globe className="h-4 w-4 mr-2" />
                        <span>Update GeoIP Data</span>
                      </DropdownMenuItem>

                      <DropdownMenuItem onClick={handleDeduplicateVisitors}>
                        <User className="h-4 w-4 mr-2" />
                        <span>Deduplicate Visitors</span>
                      </DropdownMenuItem>

                      <DropdownMenuSeparator />

                      <DropdownMenuItem
                        onClick={() => setIsDeleteAllDialogOpen(true)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        <span>Delete All Records</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="p-3 sm:p-4">
                {/* Mobile-Optimized Visitor filters */}
                <div className="mb-3 sm:mb-4 p-2 sm:p-3 bg-slate-50/5 rounded-lg border border-slate-700/50">
                  <VisitorFilters
                    filters={advancedFilters}
                    onFilterChange={handleFilterChange}
                    onReset={handleResetFilters}
                    countryOptions={countryOptions}
                    deviceOptions={deviceOptions}
                    browserOptions={browserOptions}
                    osOptions={osOptions}
                  />
                </div>
                
                {/* Visitor table with loading state */}
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : visitors.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <UserX className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-xl font-medium mb-2">No visitors found</h3>
                    <p className="text-muted-foreground max-w-md mb-4">
                      {advancedFilters.search || Object.values(advancedFilters).some(v => 
                        Array.isArray(v) ? v.length > 0 : v !== null && v !== undefined && v !== ''
                      ) ? 
                        'Try adjusting your filters to see more results.' : 
                        'There are no visitor records in the database yet.'
                      }
                    </p>
                    {(advancedFilters.search || Object.values(advancedFilters).some(v => 
                      Array.isArray(v) ? v.length > 0 : v !== null && v !== undefined && v !== ''
                    )) && (
                      <Button onClick={handleResetFilters} size="sm" variant="outline">
                        <Filter className="h-4 w-4 mr-2" />
                        Reset Filters
                      </Button>
                    )}
                  </div>
                ) : (
                  // Mobile-Responsive Visitor Display
                  <>
                    {/* Desktop Table - Hidden on Mobile */}
                    <div className="hidden lg:block w-full overflow-x-auto border border-slate-700/70 rounded-md">
                      <Table className="w-full border-collapse">
                        <TableHeader>
                          <TableRow className="bg-slate-800/60 hover:bg-slate-800/70 border-b-2 border-slate-700">
                            <TableHead className="font-medium" style={{ width: '20%' }}>Visitor</TableHead>
                            <TableHead className="font-medium" style={{ width: '18%' }}>Location</TableHead>
                            <TableHead className="font-medium" style={{ width: '18%' }}>Device</TableHead>
                            <TableHead className="font-medium" style={{ width: '18%' }}>Last Activity</TableHead>
                            <TableHead className="font-medium text-center" style={{ width: '8%' }}>Visits</TableHead>
                            <TableHead className="font-medium text-center" style={{ width: '8%' }}>Pages</TableHead>
                            <TableHead className="text-right font-medium" style={{ width: '10%' }}>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {visitors.map((visitor) => (
                            <TableRow
                              key={visitor._id}
                              className="border-b border-slate-700/60 hover:bg-slate-800/30 transition-colors"
                            >
                              <TableCell className="py-3" style={{ width: '20%' }}>
                                <div className="flex items-center gap-2">
                                  <div className="rounded-full bg-vista-blue/10 p-1.5">
                                    {visitor.convertedToUser ? (
                                      <UserCheck className="h-3.5 w-3.5 text-vista-blue" />
                                    ) : (
                                      <UserRound className="h-3.5 w-3.5 text-muted-foreground" />
                                    )}
                                  </div>
                                  <div>
                                    <div className="font-medium">
                                      {visitor.nickname || visitor.visitorId.substring(0, 16)}
                                    </div>
                                    <div className="text-sm text-muted-foreground">
                                      {visitor.convertedToUser ? (
                                        <Badge variant="outline" className="bg-vista-blue/10 text-vista-blue border-0 text-sm px-1.5 py-0.5">
                                          Converted
                                        </Badge>
                                      ) : (
                                        <span className="text-sm">Anonymous</span>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                            <TableCell className="py-3" style={{ width: '18%' }}>
                              <div className="flex items-center gap-2">
                                <div>
                                  {visitor.country ? (
                                    <>
                                      <div className="font-medium">{visitor.country}</div>
                                      <div className="text-sm text-muted-foreground">
                                        {visitor.city ? `${visitor.city}${visitor.region ? `, ${visitor.region}` : ''}` : '—'}
                                      </div>
                                    </>
                                  ) : (
                                    <span className="text-muted-foreground">Unknown</span>
                                  )}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="py-3" style={{ width: '18%' }}>
                              <div className="flex items-center gap-1.5">
                                <div className="rounded-full bg-muted p-0.5">
                                  {visitor.device === 'desktop' ? (
                                    <Monitor className="h-2.5 w-2.5 text-muted-foreground" />
                                  ) : visitor.device === 'tablet' ? (
                                    <Tablet className="h-2.5 w-2.5 text-muted-foreground" />
                                  ) : visitor.device === 'mobile' ? (
                                    <Smartphone className="h-2.5 w-2.5 text-muted-foreground" />
                                  ) : (
                                    <Network className="h-2.5 w-2.5 text-muted-foreground" />
                                  )}
                                </div>
                                <div>
                                  <div className="text-sm capitalize font-medium">{visitor.device || 'Unknown'}</div>
                                  <div className="text-sm text-muted-foreground">
                                    {visitor.browser || '—'}{visitor.os ? ` / ${visitor.os}` : ''}
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="py-3" style={{ width: '18%' }}>
                              <div>
                                <div className="font-medium">
                                  {formatRelativeTime(visitor.lastVisit)}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {formatDate(visitor.lastVisit)} at {formatTime(visitor.lastVisit)}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="py-3 text-center" style={{ width: '8%' }}>
                              <Badge variant="outline" className="text-sm px-2 py-0.5">
                                {visitor.visitCount}
                              </Badge>
                            </TableCell>
                            <TableCell className="py-3 text-center" style={{ width: '8%' }}>
                              <Badge variant="outline" className="text-sm px-2 py-0.5">
                                {visitor.pagesViewed}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right py-3" style={{ width: '10%' }}>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                    <MoreHorizontal className="h-3.5 w-3.5" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem
                                    onClick={() => {
                                      // Reset state first before setting new visitor
                                      setSelectedVisitor(null);
                                      // Small delay to ensure state is reset
                                      setTimeout(() => {
                                        setSelectedVisitor(visitor);
                                        setIsViewDetailsOpen(true);
                                      }, 50);
                                    }}
                                  >
                                    <Eye className="h-4 w-4 mr-2" />
                                    <span>View Details</span>
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setVisitorToDelete(visitor.visitorId);
                                      setIsDeleteDialogOpen(true);
                                    }}
                                    className="text-red-600 focus:text-red-600"
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    <span>Delete</span>
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                    {/* Mobile Card Layout - Visible on Mobile/Tablet */}
                    <div className="lg:hidden space-y-3">
                      {visitors.map((visitor) => (
                        <Card key={visitor._id} className="border border-slate-700/50 bg-slate-800/20 hover:bg-slate-800/40 transition-colors">
                          <CardContent className="p-4">
                            <div className="flex items-start justify-between gap-3">
                              {/* Visitor Info */}
                              <div className="flex items-start gap-3 min-w-0 flex-1">
                                <div className="rounded-full bg-vista-blue/10 p-2 flex-shrink-0">
                                  {visitor.convertedToUser ? (
                                    <UserCheck className="h-4 w-4 text-vista-blue" />
                                  ) : (
                                    <UserRound className="h-4 w-4 text-muted-foreground" />
                                  )}
                                </div>
                                <div className="min-w-0 flex-1">
                                  <div className="flex items-center gap-2 mb-1">
                                    <h3 className="font-medium text-sm truncate">
                                      {visitor.nickname || visitor.visitorId.substring(0, 16)}
                                    </h3>
                                    {visitor.convertedToUser && (
                                      <Badge variant="outline" className="bg-vista-blue/10 text-vista-blue border-0 text-xs px-1.5 py-0.5 flex-shrink-0">
                                        Converted
                                      </Badge>
                                    )}
                                  </div>

                                  {/* Location & Device Info */}
                                  <div className="space-y-1 text-xs text-muted-foreground">
                                    <div className="flex items-center gap-1.5">
                                      <MapPin className="h-3 w-3 flex-shrink-0" />
                                      <span className="truncate">
                                        {visitor.country ?
                                          `${visitor.country}${visitor.city ? `, ${visitor.city}` : ''}` :
                                          'Unknown Location'
                                        }
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-1.5">
                                      {visitor.device === 'desktop' ? (
                                        <Monitor className="h-3 w-3 flex-shrink-0" />
                                      ) : visitor.device === 'tablet' ? (
                                        <Tablet className="h-3 w-3 flex-shrink-0" />
                                      ) : visitor.device === 'mobile' ? (
                                        <Smartphone className="h-3 w-3 flex-shrink-0" />
                                      ) : (
                                        <Network className="h-3 w-3 flex-shrink-0" />
                                      )}
                                      <span className="truncate">
                                        {visitor.device || 'Unknown'} • {visitor.browser || 'Unknown Browser'}
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-1.5">
                                      <Clock className="h-3 w-3 flex-shrink-0" />
                                      <span className="truncate">
                                        {formatRelativeTime(visitor.lastVisit)}
                                      </span>
                                    </div>
                                  </div>

                                  {/* Stats */}
                                  <div className="flex items-center gap-3 mt-2">
                                    <div className="flex items-center gap-1">
                                      <Eye className="h-3 w-3 text-muted-foreground" />
                                      <span className="text-xs font-medium">{visitor.visitCount} visits</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <File className="h-3 w-3 text-muted-foreground" />
                                      <span className="text-xs font-medium">{visitor.pagesViewed} pages</span>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* Actions */}
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0 flex-shrink-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent
                                  align="end"
                                  className="z-[9999] border border-vista-light/20 bg-vista-dark shadow-xl rounded-lg overflow-hidden max-h-[70vh] overflow-y-auto"
                                  sideOffset={4}
                                  collisionPadding={40}
                                  avoidCollisions={true}
                                  sticky="partial"
                                  hideWhenDetached={false}
                                  alignOffset={-8}
                                >
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setSelectedVisitor(null);
                                      setTimeout(() => {
                                        setSelectedVisitor(visitor);
                                        setIsViewDetailsOpen(true);
                                      }, 50);
                                    }}
                                  >
                                    <Eye className="h-4 w-4 mr-2" />
                                    <span>View Details</span>
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setVisitorToDelete(visitor.visitorId);
                                      setIsDeleteDialogOpen(true);
                                    }}
                                    className="text-red-600 focus:text-red-600"
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    <span>Delete</span>
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </>
                )}
                
                {/* Mobile-Optimized Pagination */}
                {!isLoading && visitors.length > 0 && (
                  <div className="mt-3 sm:mt-4 flex justify-center">
                    <Pagination
                      currentPage={pagination.page}
                      totalPages={pagination.pages}
                      onPageChange={handlePageChange}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
      
      {/* Delete Visitor Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Visitor</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this visitor? This action cannot be undone and all associated data will be permanently removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-500 hover:bg-red-600"
              onClick={() => {
                if (visitorToDelete) {
                  handleDeleteVisitor(visitorToDelete);
                }
              }}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Delete All Visitors Dialog */}
      <AlertDialog open={isDeleteAllDialogOpen} onOpenChange={setIsDeleteAllDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete All Visitors</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete ALL visitors? This action cannot be undone and all visitor data will be permanently removed from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-500 hover:bg-red-600"
              onClick={handleDeleteAllVisitors}
            >
              Delete All
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* View Visitor Details Dialog */}
      {selectedVisitor && (
        <Dialog 
          open={isViewDetailsOpen} 
          onOpenChange={(open) => {
            setIsViewDetailsOpen(open);
            // Reset selectedVisitor when dialog is closed
            if (!open) {
              // Use setTimeout to delay clearing the state until after the dialog animation completes
              setTimeout(() => {
                setSelectedVisitor(null);
              }, 300);
            }
          }}
        >
          <DialogContent className="max-w-3xl max-h-[85vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <UserRound className="h-5 w-5 text-primary" />
                Visitor Details: {selectedVisitor.nickname || selectedVisitor.visitorId.substring(0, 12) + '...'}
              </DialogTitle>
              <div className="text-sm text-muted-foreground mt-2">
                <div className="flex flex-wrap gap-2 items-center">
                  <Badge variant="outline" className="bg-slate-100/10">
                    <span className="flex items-center gap-1.5">
                      <Globe className="h-3.5 w-3.5 mr-1" />
                      {selectedVisitor.country || 'Unknown Location'}
                    </span>
                  </Badge>
                  
                  <Badge variant="outline" className="bg-slate-100/10">
                    <span className="flex items-center gap-1.5">
                      {selectedVisitor.device === 'desktop' ? (
                        <Monitor className="h-3.5 w-3.5 mr-1" />
                      ) : selectedVisitor.device === 'tablet' ? (
                        <Tablet className="h-3.5 w-3.5 mr-1" />
                      ) : selectedVisitor.device === 'mobile' ? (
                        <Smartphone className="h-3.5 w-3.5 mr-1" />
                      ) : (
                        <Network className="h-3.5 w-3.5 mr-1" />
                      )}
                      {selectedVisitor.device || 'Unknown Device'}
                    </span>
                  </Badge>
                  
                  <span>First seen {formatRelativeTime(selectedVisitor.firstVisit)}</span>
                </div>
              </div>
            </DialogHeader>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
              {/* Basic Information */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center gap-2">
                    <User className="h-4 w-4 text-primary" />
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">ID:</span>
                    <span className="font-mono">{selectedVisitor.visitorId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Nickname:</span>
                    <span>{selectedVisitor.nickname || 'Not assigned'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">First Seen:</span>
                    <span>{formatDate(selectedVisitor.firstVisit)} at {formatTime(selectedVisitor.firstVisit)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Last Seen:</span>
                    <span>{formatDate(selectedVisitor.lastVisit)} at {formatTime(selectedVisitor.lastVisit)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Total Visits:</span>
                    <span className="font-medium">{selectedVisitor.visitCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Pages Viewed:</span>
                    <span className="font-medium">{selectedVisitor.pagesViewed}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Converted to User:</span>
                    <span>
                      {selectedVisitor.convertedToUser ? (
                        <Badge variant="outline" className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 border-0">
                          Yes
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-slate-100 text-slate-700 dark:bg-slate-900/30 dark:text-slate-400 border-0">
                          No
                        </Badge>
                      )}
                    </span>
                  </div>
                  {selectedVisitor.convertedToUser && selectedVisitor.userId && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">User ID:</span>
                      <span className="font-mono">{selectedVisitor.userId}</span>
                    </div>
                  )}
                </CardContent>
              </Card>
              
              {/* Location Information */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-primary" />
                    Location Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">IP Address:</span>
                    <span className="font-mono">{selectedVisitor.ipAddress || 'Unknown'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Country:</span>
                    <span>{selectedVisitor.country || 'Unknown'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Region:</span>
                    <span>{selectedVisitor.region || 'Unknown'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">City:</span>
                    <span>{selectedVisitor.city || 'Unknown'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Timezone:</span>
                    <span>{selectedVisitor.timezone || 'Unknown'}</span>
                  </div>
                  {selectedVisitor.latitude && selectedVisitor.longitude && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Coordinates:</span>
                      <span className="font-mono">
                        {selectedVisitor.latitude.toFixed(4)}, {selectedVisitor.longitude.toFixed(4)}
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>
              
              {/* Device Information */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Laptop className="h-4 w-4 text-primary" />
                    Device Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Device Type:</span>
                    <span>{selectedVisitor.device || 'Unknown'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Browser:</span>
                    <span>{selectedVisitor.browser || 'Unknown'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">OS:</span>
                    <span>{selectedVisitor.os || 'Unknown'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">User Agent:</span>
                    <div className="max-w-[260px] truncate text-right">
                      <TooltipElement
                        content={<p className="font-mono text-xs break-all">{selectedVisitor.userAgent || 'Unknown'}</p>}
                        className="max-w-sm"
                      >
                        <span className="font-mono text-xs cursor-help">{selectedVisitor.userAgent ? selectedVisitor.userAgent.substring(0, 30) + '...' : 'Unknown'}</span>
                      </TooltipElement>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Fingerprint:</span>
                    <div className="max-w-[260px] truncate text-right">
                      <TooltipElement
                        content={<p className="font-mono text-xs break-all">{selectedVisitor.fingerprint || 'Unknown'}</p>}
                        className="max-w-sm"
                      >
                        <span className="font-mono text-xs cursor-help">{selectedVisitor.fingerprint ? selectedVisitor.fingerprint.substring(0, 10) + '...' : 'Unknown'}</span>
                      </TooltipElement>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Visit Information */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Activity className="h-4 w-4 text-primary" />
                    Visit Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Referrer:</span>
                    <span>{selectedVisitor.referrer || 'Direct'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">First Visit:</span>
                    <span>{formatDate(selectedVisitor.firstVisit)} at {formatTime(selectedVisitor.firstVisit)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Last Visit:</span>
                    <span>{formatDate(selectedVisitor.lastVisit)} at {formatTime(selectedVisitor.lastVisit)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Total Visits:</span>
                    <span className="font-medium">{selectedVisitor.visitCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Pages Viewed:</span>
                    <span className="font-medium">{selectedVisitor.pagesViewed}</span>
                  </div>
                  {selectedVisitor.createdAt && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Record Created:</span>
                      <span>{formatDate(selectedVisitor.createdAt)} at {formatTime(selectedVisitor.createdAt)}</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
            
            <DialogFooter className="mt-6 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setIsViewDetailsOpen(false);
                  // Allow dialog to close before resetting the selected visitor
                  setTimeout(() => {
                    setSelectedVisitor(null);
                  }, 300);
                }}
              >
                Close
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => {
                  setIsViewDetailsOpen(false);
                  setVisitorToDelete(selectedVisitor.visitorId);
                  setIsDeleteDialogOpen(true);
                  // Reset the selected visitor after dialog closes
                  setTimeout(() => {
                    setSelectedVisitor(null);
                  }, 300);
                }}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Visitor
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

